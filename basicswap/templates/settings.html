{% include 'header.html' %}
{% from 'style.html' import breadcrumb_line_svg, input_arrow_down_svg %}
<div class="container mx-auto">
 <section class="p-5 mt-5">
  <div class="flex flex-wrap items-center -m-2">
   <div class="w-full md:w-1/2 p-2">
    <ul class="flex flex-wrap items-center gap-x-3 mb-2">
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/">
       <p>Home</p>
      </a>
     </li>
    <li> {{ breadcrumb_line_svg | safe }} </li>
     <li>
      <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/settings">Settings</a>
     </li>
    <li> {{ breadcrumb_line_svg | safe }} </li>
    </ul>
   </div>
  </div>
 </section>
 <section class="py-3">
  <div class="container px-4 mx-auto">
   <div class="relative py-11 px-16 bg-coolGray-900 dark:bg-blue-500 rounded-md overflow-hidden">
    <img class="absolute z-10 left-4 top-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute z-10 right-4 bottom-4" src="/static/images/elements/dots-red.svg" alt="">
    <img class="absolute h-64 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 object-cover" src="/static/images/elements/wave.svg" alt="">
    <div class="relative z-20 flex flex-wrap items-center -m-3">
     <div class="w-full md:w-1/2 p-3">
      <h2 class="mb-6 text-4xl font-bold text-white tracking-tighter">Settings</h2>
      <p class="font-normal text-coolGray-200 dark:text-white">Manage your BasicSwap client and coins settings.</p>
     </div>
    </div>
   </div>
  </div>
 </section>
 {% include 'inc_messages.html' %}



 <section class="py-4">
  <div class="container px-4 mx-auto">
   <div class="bg-coolGray-100 dark:bg-gray-500 rounded-xl  ">
    <div class="">
     <nav class="flex space-x-8 px-6" aria-label="Tabs">
      <button class="tab-button border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 py-4 px-1 text-sm font-medium focus:outline-none focus:ring-0" data-tab="coins" id="coins-tab">
       Coins
      </button>
      <button class="tab-button border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 py-4 px-1 text-sm font-medium focus:outline-none focus:ring-0" data-tab="general" id="general-tab">
       General
      </button>
      <button class="tab-button border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 py-4 px-1 text-sm font-medium focus:outline-none focus:ring-0" data-tab="notifications" id="notifications-tab">
       Notifications
      </button>
      <button class="tab-button border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 py-4 px-1 text-sm font-medium focus:outline-none focus:ring-0" data-tab="tor" id="tor-tab">
       Tor
      </button>
     </nav>
    </div>
   </div>
  </div>
 </section>

 <div class="container px-4 mx-auto">
  
  <div class="tab-content" id="coins" role="tabpanel" aria-labelledby="coins-tab">
   <form method="post" id="coins-form">
    <div class="space-y-6">
     {% for c in chains %}
     <div class="bg-coolGray-100 dark:bg-gray-500 rounded-xl  overflow-hidden coin-card" data-coin="{{ c.name }}">

      <div class="px-6 py-4 cursor-pointer coin-header" data-coin="{{ c.name }}">
       <div class="flex items-center justify-between">
        <div class="flex items-center">
         <div class="flex-shrink-0">
          <img class="h-10 w-10" src="/static/images/coins/{{ c.display_name }}.png" alt="{{ c.display_name }}">
         </div>
         <div class="ml-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ c.display_name }}</h3>
         </div>
        </div>

        <div class="flex items-center space-x-4">
         {% if c.connection_type != "none" %}
         <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
          <svg class="w-2 h-2 mr-1" fill="currentColor" viewBox="0 0 8 8">
           <circle cx="4" cy="4" r="3"/>
          </svg>
          Enabled
         </span>
         {% else %}
         <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300">
          <svg class="w-2 h-2 mr-1" fill="currentColor" viewBox="0 0 8 8">
           <circle cx="4" cy="4" r="3"/>
          </svg>
          Disabled
         </span>
         {% endif %}

         <svg class="w-5 h-5 transform transition-transform toggle-arrow text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
         </svg>
        </div>
       </div>
      </div>

      <div class="coin-details hidden" id="details-{{ c.name }}">
       <div class="px-6 py-6">
        
        {% if c.connection_type %}
        <div class="mb-6">
         <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">
          Connection
         </h4>
         
         <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
           
           <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Connection Type</label>
            <div class="px-3 py-2 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg text-sm text-gray-900 dark:text-gray-100">
             {{ c.connection_type }}
            </div>
           </div>
           
           {% if c.manage_daemon is defined %}
           <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Manage Daemon</label>
            {% if c.name in ('wownero', 'monero') %}
            <div class="relative">
             <select class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="managedaemon_{{ c.name }}">
              <option value="true" {% if c.manage_daemon==true %} selected{% endif %}>True</option>
              <option value="false" {% if c.manage_daemon==false %} selected{% endif %}>False</option>
             </select>
             <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
             </div>
            </div>
            {% else %}
            <div class="px-3 py-2 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg text-sm text-gray-900 dark:text-gray-100">
             {{ c.manage_daemon }}
            </div>
            {% endif %}
           </div>
           {% endif %}
           
          </div>
         </div>
        </div>
        {% endif %}

        {% if c.name in ('wownero', 'monero') %}
        <div class="mb-6">
         <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">
          RPC Configuration
         </h4>

         <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">

           <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Daemon RPC Host</label>
            <input type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="rpchost_{{ c.name }}" value="{{ c.rpchost }}" placeholder="127.0.0.1">
           </div>

           <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Daemon RPC Port</label>
            <input type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="rpcport_{{ c.name }}" value="{{ c.rpcport }}" placeholder="18081">
           </div>

          </div>

          <div class="mb-4">
           <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Automatically Select Daemon</label>
           <div class="relative">
            <select class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="autosetdaemon_{{ c.name }}">
             <option value="true" {% if c.autosetdaemon==true %} selected{% endif %}>True</option>
             <option value="false" {% if c.autosetdaemon==false %} selected{% endif %}>False</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
             <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
             </svg>
            </div>
           </div>
          </div>

          <div>
           <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Trusted Public Nodes</label>
           <p class="text-xs text-gray-500 dark:text-gray-400 mb-2">List trusted public nodes to use if "Automatically Select Daemon" is enabled (one per line)</p>
           <textarea class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" placeholder="node.xmr.to:18081&#10;node2.xmr.to:18081" name="remotedaemonurls_{{ c.name }}" rows="4">{{ c.remotedaemonurls }}</textarea>
          </div>
         </div>
        </div>
        {% endif %}

        <div class="mb-6">
         <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">
          Advanced Settings
         </h4>

         <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">

           <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Chain Lookups</label>
            <div class="relative">
             <select class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="lookups_{{ c.name }}">
              <option value="local" {% if c.lookups=='local' %} selected{% endif %}>Local Node</option>
              <option value="explorer" {% if c.lookups=='explorer' %} selected{% endif %}>Explorer</option>
             </select>
             <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
             </div>
            </div>
           </div>

           {% if c.manage_wallet_daemon is defined %}
           <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Manage Wallet Daemon</label>
            <div class="px-3 py-2 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg text-sm text-gray-900 dark:text-gray-100">
             {{ c.manage_wallet_daemon }}
            </div>
           </div>
           {% endif %}

           {% if c.name in ('wownero', 'monero') %}
           <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Transaction Fee Priority</label>
            <div class="relative">
             <select class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="fee_priority_{{ c.name }}">
              <option value="0" {% if c.fee_priority==0 %} selected{% endif %}>Auto</option>
              <option value="1" {% if c.fee_priority==1 %} selected{% endif %}>Slow</option>
              <option value="2" {% if c.fee_priority==2 %} selected{% endif %}>Normal</option>
              <option value="3" {% if c.fee_priority==3 %} selected{% endif %}>Fast</option>
             </select>
             <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
             </div>
            </div>
           </div>
           {% else %}
           <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Blocks Confirmed Target</label>
            <input type="number" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="conf_target_{{ c.name }}" min="1" max="32" value="{{ c.conf_target }}" placeholder="6">
           </div>
           {% endif %}

           {% if c.name == 'particl' %}
           <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Anon Tx Ring Size</label>
            <input type="number" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="rct_ring_size_{{ c.name }}" min="3" max="32" value="{{ c.anon_tx_ring_size }}" placeholder="12">
           </div>
           {% endif %}

          </div>
         </div>
        </div>

        <div class="flex justify-end space-x-3 pt-4 ">
         <button name="apply_{{ c.name }}" value="Apply" type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none">
          Apply Changes
         </button>
         {% if c.can_disable == true %}
         <button name="disable_{{ c.name }}" value="Disable" onclick="return confirmPopup('Disable', '{{ c.display_name }}');" type="submit" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none">
          Disable Coin
         </button>
         {% endif %}
         {% if c.can_reenable == true %}
         <button name="enable_{{ c.name }}" value="Enable" onclick="return confirmPopup('Enable', '{{ c.display_name }}');" type="submit" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none">
          Enable Coin
         </button>
         {% endif %}
        </div>

       </div>
      </div>

     </div>
     {% endfor %}
    </div>
    <input type="hidden" name="formid" value="{{ form_id }}">
   </form>
  </div>

  <div class="tab-content hidden" id="general" role="tabpanel" aria-labelledby="general-tab">
   <form method="post" id="general-form">
    <div class="space-y-6">

     <div class="bg-coolGray-100 dark:bg-gray-500 rounded-xl  ">
      <div class="px-6 py-4 ">
       <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        Debug Settings
       </h3>
       <div class="mt-2 p-4 bg-red-50 border border-red-400 rounded-lg dark:bg-gray-500 dark:text-red-400" role="alert">
        <div class="flex items-start">
         <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
           <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
          </svg>
         </div>
         <div class="ml-3">
          <p class="text-sm font-medium text-red-800 dark:text-red-400">
           WARNING: Advanced features - Only enable if you know what you're doing!
          </p>
         </div>
        </div>
       </div>
      </div>

      <div class="px-6 py-6">
       <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

        <div>
         <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Debug Mode</label>
         <div class="relative">
          <select name="debugmode" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none">
           <option {% if general_settings.debug %}selected{% endif %} value="True">True</option>
           <option {% if not general_settings.debug %}selected{% endif %} value="False">False</option>
          </select>
          <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
           <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
           </svg>
          </div>
         </div>
         <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Enable detailed logging and debug information</p>
        </div>

        <div>
         <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Debug UI</label>
         <div class="relative">
          <select name="debugui" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none">
           <option {% if general_settings.debug_ui %}selected{% endif %} value="True">True</option>
           <option {% if not general_settings.debug_ui %}selected{% endif %} value="False">False</option>
          </select>
          <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
           <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
           </svg>
          </div>
         </div>
         <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Show advanced UI elements and debug controls</p>
        </div>

        <div class="md:col-span-2">
         <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Remove DB Records for Expired Offers</label>
         <div class="relative max-w-xs">
          <select name="expire_db_records" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none">
           <option {% if general_settings.expire_db_records %}selected{% endif %} value="True">True</option>
           <option {% if not general_settings.expire_db_records %}selected{% endif %} value="False">False</option>
          </select>
          <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
           <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
           </svg>
          </div>
         </div>
         <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Automatically clean up database records for expired offers</p>
        </div>

       </div>

       <div class="flex justify-end pt-6  mt-6">
        <button name="apply_general" value="Apply" type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none">
         Apply Changes
        </button>
       </div>
      </div>
     </div>

     <div class="bg-coolGray-100 dark:bg-gray-500 rounded-xl  ">
      <div class="px-6 py-4 ">
       <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        Charts Configuration
       </h3>
      </div>

      <div class="px-6 py-6">
       <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

        <div>
         <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Show Charts</label>
         <div class="relative">
          <select name="showchart" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none">
           <option {% if chart_settings.show_chart %}selected{% endif %} value="True">True</option>
           <option {% if not chart_settings.show_chart %}selected{% endif %} value="False">False</option>
          </select>
          <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
           <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
           </svg>
          </div>
         </div>
         <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Enable price charts in the interface</p>
        </div>

        <div>
         <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">CryptoCompare API Key</label>
         <input type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="chartapikey" value="{{ chart_settings.chart_api_key }}" placeholder="Enter API key">
         <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">API key for CryptoCompare price data</p>
        </div>

        <div class="md:col-span-2">
         <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">CoinGecko API Key</label>
         <div class="max-w-md">
          <input type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="coingeckoapikey" value="{{ chart_settings.coingecko_api_key }}" placeholder="Enter API key">
         </div>
         <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">API key for CoinGecko price data (optional for basic usage)</p>
        </div>

        <div class="md:col-span-2">
         <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Enabled Coins</label>
         <input type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="enabledchartcoins" value="{{ chart_settings.enabled_chart_coins }}" placeholder="Leave blank for active coins, 'all' for all coins, or comma-separated list">
         <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Coins to show data for: Blank for active coins, "all" for all known coins or comma separated list of coin tickers to show</p>
        </div>

       </div>

       <div class="flex justify-end pt-6  mt-6">
        <button name="apply_chart" value="Apply" type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none">
         Apply Changes
        </button>
       </div>
      </div>
     </div>
    </div>
    <input type="hidden" name="formid" value="{{ form_id }}">
   </form>
  </div>

  <div class="tab-content hidden" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
   <form method="post">
    <div class="space-y-6">

     <div class="bg-coolGray-100 dark:bg-gray-500 rounded-xl">
      <div class="px-6 py-4">
       <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        Notification Settings
       </h3>
       <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">
        Configure which notifications you want to see.
       </p>
      </div>
      <div class="px-6 pb-6 space-y-6">

       <div>
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Notification Types</h4>
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
         <div class="space-y-4">

          <div class="py-2">
           <div class="flex items-center">
            <input type="checkbox" id="notifications_new_offers" name="notifications_new_offers" value="true" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-600 dark:border-gray-500"{% if notification_settings.notifications_new_offers %} checked{% endif %}>
            <label for="notifications_new_offers" class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">New Offers</label>
           </div>
           <p class="text-xs text-gray-500 dark:text-gray-400 ml-7 mt-1">Show notifications for new network offers</p>
          </div>

          <div class="py-2">
           <div class="flex items-center">
            <input type="checkbox" id="notifications_new_bids" name="notifications_new_bids" value="true" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-600 dark:border-gray-500"{% if notification_settings.notifications_new_bids %} checked{% endif %}>
            <label for="notifications_new_bids" class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">New Bids</label>
           </div>
           <p class="text-xs text-gray-500 dark:text-gray-400 ml-7 mt-1">Show notifications for new bids on your offers</p>
          </div>

          <div class="py-2">
           <div class="flex items-center">
            <input type="checkbox" id="notifications_bid_accepted" name="notifications_bid_accepted" value="true" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-600 dark:border-gray-500"{% if notification_settings.notifications_bid_accepted %} checked{% endif %}>
            <label for="notifications_bid_accepted" class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">Bid Accepted</label>
           </div>
           <p class="text-xs text-gray-500 dark:text-gray-400 ml-7 mt-1">Show notifications when your bids are accepted</p>
          </div>

          <div class="py-2">
           <div class="flex items-center">
            <input type="checkbox" id="notifications_balance_changes" name="notifications_balance_changes" value="true" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-600 dark:border-gray-500"{% if notification_settings.notifications_balance_changes %} checked{% endif %}>
            <label for="notifications_balance_changes" class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">Balance Changes</label>
           </div>
           <p class="text-xs text-gray-500 dark:text-gray-400 ml-7 mt-1">Show notifications for incoming and outgoing funds</p>
          </div>

          <div class="py-2">
           <div class="flex items-center">
            <input type="checkbox" id="notifications_outgoing_transactions" name="notifications_outgoing_transactions" value="true" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-600 dark:border-gray-500"{% if notification_settings.notifications_outgoing_transactions %} checked{% endif %}>
            <label for="notifications_outgoing_transactions" class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">Outgoing Transactions</label>
           </div>
           <p class="text-xs text-gray-500 dark:text-gray-400 ml-7 mt-1">Show notifications for sent funds</p>
          </div>

          <div class="py-2">
           <div class="flex items-center">
            <input type="checkbox" id="notifications_swap_completed" name="notifications_swap_completed" value="true" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-600 dark:border-gray-500"{% if notification_settings.notifications_swap_completed %} checked{% endif %}>
            <label for="notifications_swap_completed" class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">Swap Completed</label>
           </div>
           <p class="text-xs text-gray-500 dark:text-gray-400 ml-7 mt-1">Show notifications when swaps complete successfully</p>
          </div>



         </div>
        </div>
       </div>

       <div>
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Display Duration</h4>
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
         <div>
          <label for="notifications_duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Duration</label>
          <div class="relative w-48">
           <select id="notifications_duration" name="notifications_duration" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none">
            <option value="5"{% if notification_settings.notifications_duration == 5 %} selected{% endif %}>5 seconds</option>
            <option value="10"{% if notification_settings.notifications_duration == 10 %} selected{% endif %}>10 seconds</option>
            <option value="15"{% if notification_settings.notifications_duration == 15 %} selected{% endif %}>15 seconds</option>
            <option value="20"{% if notification_settings.notifications_duration == 20 %} selected{% endif %}>20 seconds</option>
            <option value="30"{% if notification_settings.notifications_duration == 30 %} selected{% endif %}>30 seconds</option>
            <option value="60"{% if notification_settings.notifications_duration == 60 %} selected{% endif %}>1 minute</option>
           </select>
           <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
           </div>
          </div>
         </div>
        </div>
       </div>

       {% if general_settings.debug_ui %}
       <div>
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Test Notifications</h4>
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
         <div>
          <button type="button" onclick="window.NotificationManager && window.NotificationManager.testToasts()" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none">
           Test All Notification Types
          </button>
         </div>
        </div>
       </div>
       {% endif %}

       <div class="flex justify-end mt-6">
        <button name="apply_notifications" value="Apply" type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none">
         Apply Changes
        </button>
       </div>

      </div>
     </div>

    </div>
    <input type="hidden" name="formid" value="{{ form_id }}">
   </form>
  </div>

  <script>
    function syncNotificationSettings() {
      if (window.NotificationManager && typeof window.NotificationManager.updateSettings === 'function') {
        const backendSettings = {
          showNewOffers: document.getElementById('notifications_new_offers').checked,
          showNewBids: document.getElementById('notifications_new_bids').checked,
          showBidAccepted: document.getElementById('notifications_bid_accepted').checked,
          showBalanceChanges: document.getElementById('notifications_balance_changes').checked,
          showOutgoingTransactions: document.getElementById('notifications_outgoing_transactions').checked,
          showSwapCompleted: document.getElementById('notifications_swap_completed').checked,
          notificationDuration: parseInt(document.getElementById('notifications_duration').value) * 1000
        };

        window.NotificationManager.updateSettings(backendSettings);
      }
    }

    document.getElementById('notifications-tab').addEventListener('click', function() {
      setTimeout(syncNotificationSettings, 100);
    });

    document.addEventListener('DOMContentLoaded', function() {
      syncNotificationSettings();
    });

    document.addEventListener('change', function(e) {
      if (e.target.closest('#notifications')) {
        syncNotificationSettings();
      }
    });
  </script>

  <div class="tab-content hidden" id="tor" role="tabpanel" aria-labelledby="tor-tab">
   <form method="post" id="tor-form">
    <div class="space-y-6">

     <div class="bg-coolGray-100 dark:bg-gray-500 rounded-xl  ">
      <div class="px-6 py-4 ">
       <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        Tor Configuration
       </h3>
      </div>

      <div class="px-6 py-6">
       <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

        <div>
         <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Use Tor Proxy</label>
         <div class="relative">
          <select name="usetorproxy" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none">
           <option {% if tor_settings.use_tor %}selected{% endif %} value="true">Enabled</option>
           <option {% if not tor_settings.use_tor %}selected{% endif %} value="false">Disabled</option>
          </select>
          <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
           <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
           </svg>
          </div>
         </div>
         <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Enable Tor proxy for enhanced privacy</p>
        </div>

        <div>
         <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tor Proxy Host</label>
         <input type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="proxyhost" value="{{ tor_settings.proxy_host }}" placeholder="127.0.0.1">
        </div>

        <div>
         <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tor Proxy Port</label>
         <input type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="proxyport" value="{{ tor_settings.proxy_port }}" placeholder="9050">
        </div>

        <div>
         <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tor Control Port</label>
         <input type="text" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="controlport" value="{{ tor_settings.control_port }}" placeholder="9051">
        </div>

        <div class="md:col-span-2">
         <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tor Control Password</label>
         <div class="max-w-md">
          <input type="password" class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-1 focus:outline-none" name="controlpwd" value="{{ tor_settings.control_password }}" placeholder="Enter password">
         </div>
         <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Password for Tor control interface (optional)</p>
        </div>

       </div>

       <div class="flex justify-end pt-6  mt-6">
        <button name="apply_tor" value="Apply" type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none">
         Apply Changes
        </button>
       </div>
      </div>
     </div>
    </div>
    <input type="hidden" name="formid" value="{{ form_id }}">
   </form>
  </div>

 </div>
</div>

<div id="confirmModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
  <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity duration-300 ease-out"></div>
  <div class="relative z-50 min-h-screen px-4 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-500 rounded-lg max-w-md w-full p-6 shadow-lg transition-opacity duration-300 ease-out">
      <div class="text-center">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4" id="confirmTitle">Confirm Action</h2>
        <p class="text-gray-600 dark:text-gray-200 mb-6 whitespace-pre-line" id="confirmMessage">Are you sure?</p>
        <div class="flex justify-center gap-4">
          <button type="button" id="confirmYes"
                  class="px-4 py-2.5 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
            Confirm
          </button>
          <button type="button" id="confirmNo"
                  class="px-4 py-2.5 font-medium text-sm text-white hover:text-red border border-red-500 hover:border-red-500 hover:bg-red-600 bg-red-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
 const tabButtons = document.querySelectorAll('.tab-button');
 const tabContents = document.querySelectorAll('.tab-content');

 function switchTab(targetTab) {
  tabButtons.forEach(btn => {
   if (btn.dataset.tab === targetTab) {
    btn.className = 'tab-button border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 py-4 px-1 text-sm font-medium focus:outline-none focus:ring-0';
   } else {
    btn.className = 'tab-button border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 py-4 px-1 text-sm font-medium focus:outline-none focus:ring-0';
   }
  });

  tabContents.forEach(content => {
   if (content.id === targetTab) {
    content.classList.remove('hidden');
   } else {
    content.classList.add('hidden');
   }
  });
 }

 tabButtons.forEach(btn => {
  btn.addEventListener('click', () => {
   switchTab(btn.dataset.tab);
  });
 });

 const coinHeaders = document.querySelectorAll('.coin-header');
 coinHeaders.forEach(header => {
  header.addEventListener('click', function() {
   const coinName = this.dataset.coin;
   const details = document.getElementById(`details-${coinName}`);
   const arrow = this.querySelector('.toggle-arrow');

   if (details.classList.contains('hidden')) {
    details.classList.remove('hidden');
    arrow.style.transform = 'rotate(180deg)';
   } else {
    details.classList.add('hidden');
    arrow.style.transform = 'rotate(0deg)';
   }
  });
 });

 let confirmCallback = null;
 let triggerElement = null;

 document.getElementById('confirmYes').addEventListener('click', function() {
  if (typeof confirmCallback === 'function') {
   confirmCallback();
  }
  hideConfirmDialog();
 });

 document.getElementById('confirmNo').addEventListener('click', hideConfirmDialog);

 function showConfirmDialog(title, message, callback) {
  confirmCallback = callback;
  document.getElementById('confirmTitle').textContent = title;
  document.getElementById('confirmMessage').textContent = message;
  const modal = document.getElementById('confirmModal');
  if (modal) {
   modal.classList.remove('hidden');
  }
  return false;
 }

 function hideConfirmDialog() {
  const modal = document.getElementById('confirmModal');
  if (modal) {
   modal.classList.add('hidden');
  }
  confirmCallback = null;
  return false;
 }

 window.confirmPopup = function(action, coin_name) {
  triggerElement = document.activeElement;
  const title = `Confirm ${action} ${coin_name}`;
  const message = `Are you sure you want to ${action.toLowerCase()} ${coin_name}?\n\nThis will shutdown BasicSwap.`;

  return showConfirmDialog(title, message, function() {
   if (triggerElement) {
    const form = triggerElement.form;
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.name = triggerElement.name;
    hiddenInput.value = triggerElement.value;
    form.appendChild(hiddenInput);
    form.submit();
   }
  });
 };

 const overrideButtonConfirm = function(button, action, coinName) {
  if (button) {
   button.removeAttribute('onclick');
   button.addEventListener('click', function(e) {
    e.preventDefault();
    triggerElement = this;
    return confirmPopup(action, coinName);
   });
  }
 };

 const disableButtons = document.querySelectorAll('button[name^="disable_"]');
 disableButtons.forEach(btn => {
  const coinName = btn.name.replace('disable_', '');
  const displayName = btn.closest('.coin-card').querySelector('h3').textContent.split(' (')[0];
  overrideButtonConfirm(btn, 'Disable', displayName);
 });

 const enableButtons = document.querySelectorAll('button[name^="enable_"]');
 enableButtons.forEach(btn => {
  const coinName = btn.name.replace('enable_', '');
  const displayName = btn.closest('.coin-card').querySelector('h3').textContent.split(' (')[0];
  overrideButtonConfirm(btn, 'Enable', displayName);
 });

 switchTab('coins');
});


</script>

{% include 'footer.html' %}
